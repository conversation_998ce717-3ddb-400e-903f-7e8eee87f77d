2025-07-01 15:07:09,079 INFO ipython === bench console session ===
2025-07-01 15:07:09,079 INFO ipython frappe.db.sql("SELECT 1 as test")
2025-07-01 15:07:09,079 INFO ipython frappe.get_all("User", limit=3)
2025-07-01 15:07:09,079 INFO ipython frappe.get_installed_apps()
2025-07-01 15:07:09,079 INFO ipython frappe.db.sql("SHOW TABLES LIKE '%Product%'")
2025-07-01 15:07:09,079 INFO ipython frappe.db.sql("SHOW TABLES LIKE '%Ecommerce%'")
2025-07-01 15:07:09,079 INFO ipython frappe.db.sql("SHOW TABLES LIKE '%Shopping%'")
2025-07-01 15:07:09,080 INFO ipython === session end ===
2025-07-01 15:26:49,301 INFO ipython === bench console session ===
2025-07-01 15:26:49,302 INFO ipython frappe.get_installed_apps()
2025-07-01 15:26:49,302 INFO ipython frappe.db.sql("SHOW TABLES LIKE '%Ecommerce%'")
2025-07-01 15:26:49,302 INFO ipython frappe.db.sql("SELECT 1 as test")
2025-07-01 15:26:49,302 INFO ipython === session end ===
2025-07-01 15:32:34,207 INFO ipython === bench console session ===
2025-07-01 15:32:34,207 INFO ipython frappe.get_installed_apps()
2025-07-01 15:32:34,207 INFO ipython frappe.db.sql("SHOW TABLES LIKE '%Ecommerce%'")
2025-07-01 15:32:34,207 INFO ipython frappe.db.sql("SHOW TABLES LIKE '%Product%'")
2025-07-01 15:32:34,207 INFO ipython product = frappe.new_doc("Product")
2025-07-01 15:32:34,208 INFO ipython product.name = "TEST-PRODUCT-001"
2025-07-01 15:32:34,208 INFO ipython product.title = "Test Product"
2025-07-01 15:32:34,208 INFO ipython product.description = "This is a test product"
2025-07-01 15:32:34,208 INFO ipython product.price = 99.99
2025-07-01 15:32:34,208 INFO ipython product.save()
2025-07-01 15:32:34,208 INFO ipython print(f"Created product: {product.name}")
2025-07-01 15:32:34,208 INFO ipython product = frappe.new_doc("Product")
2025-07-01 15:32:34,208 INFO ipython product.product_name = "Test Product"
2025-07-01 15:32:34,208 INFO ipython product.category = "Electronics"  # Let's try a category
2025-07-01 15:32:34,208 INFO ipython product.description = "This is a test product"
2025-07-01 15:32:34,208 INFO ipython product.price = 99.99
2025-07-01 15:32:34,208 INFO ipython product.save()
2025-07-01 15:32:34,209 INFO ipython print(f"Created product: {product.name}")
2025-07-01 15:32:34,209 INFO ipython # First create a category
2025-07-01 15:32:34,209 INFO ipython category = frappe.new_doc("Product Category")
2025-07-01 15:32:34,209 INFO ipython category.category_name = "Electronics"
2025-07-01 15:32:34,209 INFO ipython category.description = "Electronic products"
2025-07-01 15:32:34,209 INFO ipython category.save()
2025-07-01 15:32:34,209 INFO ipython print(f"Created category: {category.name}")
2025-07-01 15:32:34,209 INFO ipython # Now create the product
2025-07-01 15:32:34,209 INFO ipython product = frappe.new_doc("Product")
2025-07-01 15:32:34,209 INFO ipython product.product_name = "Test Product"
2025-07-01 15:32:34,209 INFO ipython product.category = category.name
2025-07-01 15:32:34,209 INFO ipython product.description = "This is a test product"
2025-07-01 15:32:34,210 INFO ipython product.price = 99.99
2025-07-01 15:32:34,210 INFO ipython product.save()
2025-07-01 15:32:34,210 INFO ipython print(f"Created product: {product.name}")
2025-07-01 15:32:34,210 INFO ipython # Test querying our data
2025-07-01 15:32:34,210 INFO ipython products = frappe.get_all("Product", fields=["name", "product_name", "price", "category"])
2025-07-01 15:32:34,210 INFO ipython print("Products:", products)
2025-07-01 15:32:34,210 INFO ipython categories = frappe.get_all("Product Category", fields=["name", "category_name"])
2025-07-01 15:32:34,210 INFO ipython print("Categories:", categories)
2025-07-01 15:32:34,210 INFO ipython === session end ===
2025-07-01 15:42:42,746 INFO ipython === bench console session ===
2025-07-01 15:42:42,746 INFO ipython frappe.get_installed_apps()
2025-07-01 15:42:42,746 INFO ipython frappe.db.sql("SHOW TABLES LIKE '%Ecommerce%'")
2025-07-01 15:42:42,746 INFO ipython frappe.db.sql("SHOW TABLES LIKE '%Product%'")
2025-07-01 15:42:42,746 INFO ipython # Test our existing product
2025-07-01 15:42:42,746 INFO ipython products = frappe.get_all("Product", fields=["name", "product_name", "price"])
2025-07-01 15:42:42,747 INFO ipython print("Existing products:", products)
2025-07-01 15:42:42,747 INFO ipython # Create a new test product
2025-07-01 15:42:42,747 INFO ipython product = frappe.new_doc("Product")
2025-07-01 15:42:42,747 INFO ipython product.product_name = "Test Laptop"
2025-07-01 15:42:42,747 INFO ipython product.description = "High-performance laptop for developers"
2025-07-01 15:42:42,747 INFO ipython product.price = 1299.99
2025-07-01 15:42:42,747 INFO ipython product.save()
2025-07-01 15:42:42,747 INFO ipython print(f"Created product: {product.name} - {product.product_name}")
2025-07-01 15:42:42,747 INFO ipython # Create a category first
2025-07-01 15:42:42,747 INFO ipython category = frappe.new_doc("Product Category")
2025-07-01 15:42:42,747 INFO ipython category.category_name = "Electronics"
2025-07-01 15:42:42,747 INFO ipython category.description = "Electronic devices and gadgets"
2025-07-01 15:42:42,747 INFO ipython category.save()
2025-07-01 15:42:42,747 INFO ipython print(f"Created category: {category.name}")
2025-07-01 15:42:42,747 INFO ipython # Now create the product with category
2025-07-01 15:42:42,747 INFO ipython product = frappe.new_doc("Product")
2025-07-01 15:42:42,747 INFO ipython product.product_name = "Test Laptop"
2025-07-01 15:42:42,748 INFO ipython product.category = category.name
2025-07-01 15:42:42,748 INFO ipython product.description = "High-performance laptop for developers"
2025-07-01 15:42:42,748 INFO ipython product.price = 1299.99
2025-07-01 15:42:42,748 INFO ipython product.save()
2025-07-01 15:42:42,748 INFO ipython print(f"Created product: {product.name} - {product.product_name}")
2025-07-01 15:42:42,748 INFO ipython # Test querying our data
2025-07-01 15:42:42,748 INFO ipython products = frappe.get_all("Product", fields=["name", "product_name", "price", "category"])
2025-07-01 15:42:42,748 INFO ipython print("Products:", products)
2025-07-01 15:42:42,748 INFO ipython categories = frappe.get_all("Product Category", fields=["name", "category_name"])
2025-07-01 15:42:42,748 INFO ipython print("Categories:", categories)
2025-07-01 15:42:42,748 INFO ipython === session end ===
2025-07-01 15:53:11,510 INFO ipython === bench console session ===
2025-07-01 15:53:11,511 INFO ipython === session end ===
2025-07-01 15:59:43,180 INFO ipython === bench console session ===
2025-07-01 15:59:43,180 INFO ipython === session end ===
